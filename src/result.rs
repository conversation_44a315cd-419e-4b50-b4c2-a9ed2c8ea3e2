use crate::llm::{ChatMessage, ChatRole, MessageType}; // Already using our own
use crate::task::Task;
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};
use std::path::PathBuf; // Added for SerializableTask From impl

#[derive(Serialize, Deserialize, Debug, Clone, PartialEq)]
pub enum SerializableChatRole {
    User,
    Assistant,
    System, // Added System to match our new ChatRole
}

impl From<ChatRole> for SerializableChatRole {
    fn from(role: ChatRole) -> Self {
        match role {
            ChatRole::User => SerializableChatRole::User,
            ChatRole::Assistant => SerializableChatRole::Assistant,
            ChatRole::System => SerializableChatRole::System,
        }
    }
}

impl From<SerializableChatRole> for ChatRole {
    fn from(role: SerializableChatRole) -> Self {
        match role {
            SerializableChatRole::User => ChatRole::User,
            SerializableChatRole::Assistant => ChatRole::Assistant,
            SerializableChatRole::System => ChatRole::System,
        }
    }
}

#[derive(Serialize, Deserialize, Debug, Clone, PartialEq)]
pub enum SerializableMessageType {
    Text,
}

impl From<MessageType> for SerializableMessageType {
    fn from(msg_type: MessageType) -> Self {
        match msg_type {
            MessageType::Text => SerializableMessageType::Text,
            // If MessageType enum grows, this needs to be updated
        }
    }
}

impl From<SerializableMessageType> for MessageType {
    fn from(msg_type: SerializableMessageType) -> Self {
        match msg_type {
            SerializableMessageType::Text => MessageType::Text,
        }
    }
}

#[derive(Serialize, Deserialize, Debug, Clone, PartialEq)]
pub struct SerializableChatMessage {
    pub role: SerializableChatRole,
    pub content: String,
    pub message_type: SerializableMessageType,
}

// Convert from our new &crate::llm::ChatMessage to SerializableChatMessage
impl From<&ChatMessage> for SerializableChatMessage {
    fn from(msg: &ChatMessage) -> Self {
        SerializableChatMessage {
            role: msg.role.clone().into(), // Relies on From<ChatRole> for SerializableChatRole
            content: msg.content.clone(),
            message_type: msg.message_type.clone().into(), // Relies on From<MessageType> for SerializableMessageType
        }
    }
}

// Convert from SerializableChatMessage to our new crate::llm::ChatMessage
impl From<SerializableChatMessage> for ChatMessage {
    fn from(msg: SerializableChatMessage) -> Self {
        ChatMessage {
            role: msg.role.into(), // Relies on From<SerializableChatRole> for ChatRole
            content: msg.content,
            message_type: msg.message_type.into(), // Relies on From<SerializableMessageType> for MessageType
        }
    }
}

// Added SerializableTask
use std::collections::HashSet; // Removed HashMap

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct SerializableAppliedEdit {
    pub file_path: PathBuf,
    pub start_line: usize,
    pub end_line: usize,
    pub new_code: String,
}

impl From<&crate::task::AppliedEdit> for SerializableAppliedEdit {
    fn from(edit: &crate::task::AppliedEdit) -> Self {
        Self {
            file_path: edit.file_path.clone(),
            start_line: edit.start_line,
            end_line: edit.end_line,
            new_code: edit.new_code.clone(),
        }
    }
}

impl From<SerializableAppliedEdit> for crate::task::AppliedEdit {
    fn from(s_edit: SerializableAppliedEdit) -> Self {
        Self {
            file_path: s_edit.file_path,
            start_line: s_edit.start_line,
            end_line: s_edit.end_line,
            new_code: s_edit.new_code,
        }
    }
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct SerializableTask {
    pub id: String,
    pub initial_user_prompt: String,
    pub initial_task_info: Option<String>,
    pub messages: Vec<SerializableChatMessage>,
    pub summary: Option<String>,
    pub edited_files_paths: HashSet<PathBuf>,
    pub applied_edits: Vec<SerializableAppliedEdit>,
}

// Convert from crate::task::Task to SerializableTask
impl From<&Task> for SerializableTask {
    fn from(task: &Task) -> Self {
        SerializableTask {
            id: task.id.clone(),
            initial_user_prompt: task.initial_user_prompt.clone(),
            initial_task_info: task.initial_task_info.clone(),
            messages: task
                .messages
                .iter()
                .map(SerializableChatMessage::from)
                .collect(),
            summary: task.summary.clone(),
            edited_files_paths: task.edited_files_paths.clone(),
            applied_edits: task
                .applied_edits
                .iter()
                .map(SerializableAppliedEdit::from)
                .collect(),
        }
    }
}

// Convert from SerializableTask to crate::task::Task
impl From<SerializableTask> for Task {
    fn from(st: SerializableTask) -> Self {
        Task {
            id: st.id,
            initial_user_prompt: st.initial_user_prompt,
            initial_task_info: st.initial_task_info,
            messages: st.messages.into_iter().map(ChatMessage::from).collect(),
            summary: st.summary,
            edited_files_paths: st.edited_files_paths,
            applied_edits: st
                .applied_edits
                .into_iter()
                .map(crate::task::AppliedEdit::from)
                .collect(),
        }
    }
}

#[derive(Serialize, Deserialize, Debug, Clone, PartialEq)] // Added PartialEq
pub struct SerializableQuestionAnswerPair {
    pub question: String,
    pub answer: String,
}

// Convert from interactive::app::QuestionAnswerPair to SerializableQuestionAnswerPair
impl From<&crate::interactive::app::QuestionAnswerPair> for SerializableQuestionAnswerPair {
    fn from(qna_pair: &crate::interactive::app::QuestionAnswerPair) -> Self {
        SerializableQuestionAnswerPair {
            question: qna_pair.question.clone(),
            answer: qna_pair.answer.clone(),
        }
    }
}

/// Handles storage and formatting of results from LLM operations
#[derive(Serialize, Deserialize, Debug, Clone)] // Added Deserialize
pub struct Result {
    // Renamed from ResultHandler
    pub tasks: Vec<SerializableTask>,
    pub files_edited: Vec<SerializableFileEdit>, // Renamed FileEdit
    pub question_answer_history: Vec<SerializableQuestionAnswerPair>, // Added field
    pub research_discovered_files: Option<Vec<PathBuf>>, // Added for simple research output
    pub total_retries_for_last_edit_task: Option<usize>, // Added for simple edit output
    pub total_duration_ms_for_last_edit_task: Option<u128>, // Added for simple edit output
}

/// Represents a single file edit operation
#[derive(Debug, Clone, Serialize, Deserialize)] // Added Serialize, Deserialize
pub struct SerializableFileEdit {
    // Renamed from FileEdit
    pub file_path: PathBuf,
    pub start_line: usize,
    pub end_line: usize,
    pub new_code: String,
}

impl Result {
    // Renamed from ResultHandler
    /// Create a new Result with empty collections
    pub fn new() -> Self {
        Self {
            tasks: Vec::new(),
            files_edited: Vec::new(),
            question_answer_history: Vec::new(), // Initialize new field
            research_discovered_files: None,     // Initialize new field
            total_retries_for_last_edit_task: None, // Initialize new field
            total_duration_ms_for_last_edit_task: None, // Initialize new field
        }
    }

    // add_chat_message is removed. Messages are part of tasks.
    // Tasks will be added to Result likely at the end of processing.
    // For now, let's add a method to add a whole task.
    pub fn add_task(&mut self, task: &Task) {
        self.tasks.push(task.into());
    }

    // Record a file edit operation
    // Arguments
    // file_path - Path to the edited file
    // start_line - Start line number (1-based)
    // end_line - End line number (1-based)
    // new_code - The new code for this range
    pub fn add_file_edit(
        &mut self,
        file_path: PathBuf,
        start_line: usize,
        end_line: usize,
        new_code: String,
    ) {
        self.files_edited.push(SerializableFileEdit {
            // Use renamed struct
            file_path,
            start_line,
            end_line,
            new_code,
        });
    }

    /// Convert the results to a JSON value
    pub fn to_json(&self) -> Value {
        json!({
            "tasks": self.tasks,
            "files_edited": self.files_edited, // Now directly serializable
            "question_answer_history": self.question_answer_history, // Add new field
            // research_discovered_files, total_retries, total_duration are not part of advanced JSON
        })
    }

    pub fn to_simple_output_string(
        &self,
        last_command_type: Option<crate::interactive::app::LastSignificantCommandType>,
    ) -> String {
        match last_command_type {
            Some(crate::interactive::app::LastSignificantCommandType::Research) => {
                if let Some(files) = &self.research_discovered_files {
                    if files.is_empty() {
                        "Research complete. No files were discovered.".to_string()
                    } else {
                        let file_list: Vec<String> =
                            files.iter().map(|p| p.display().to_string()).collect();
                        format!(
                            "Research complete. Discovered files:\n- {}",
                            file_list.join("\n- ")
                        )
                    }
                } else {
                    "Research complete. File discovery information not available.".to_string()
                }
            }
            Some(crate::interactive::app::LastSignificantCommandType::Question) => {
                if let Some(last_qna) = self.question_answer_history.last() {
                    last_qna.answer.clone()
                } else {
                    "No question was asked or no answer recorded.".to_string()
                }
            }
            Some(crate::interactive::app::LastSignificantCommandType::Edit) | None => {
                // Default to edit summary if type is None but tasks exist
                let mut output = String::new();
                output.push_str("Editing Completed!\n");

                if self.files_edited.is_empty() {
                    output.push_str("  - No files were edited.\n");
                } else {
                    let mut edits_summary: std::collections::HashMap<PathBuf, usize> =
                        std::collections::HashMap::new();
                    for edit in &self.files_edited {
                        *edits_summary.entry(edit.file_path.clone()).or_insert(0) += 1;
                    }
                    for (path, count) in edits_summary {
                        output.push_str(&format!("  - {} edits to {}\n", count, path.display()));
                    }
                }
                output.push('\n');

                if let Some(last_task) = self.tasks.last() {
                    if let Some(summary) = &last_task.summary {
                        output.push_str(&format!("LLM Summary: {}\n", summary));
                    } else {
                        output.push_str("LLM Summary: Not available.\n");
                    }
                } else {
                    output.push_str("LLM Summary: No task data available.\n");
                }

                if let Some(retries) = self.total_retries_for_last_edit_task {
                    output.push_str(&format!("Total Retries: {}\n", retries));
                }
                if let Some(duration_ms) = self.total_duration_ms_for_last_edit_task {
                    let secs = duration_ms / 1000;
                    let mins = secs / 60;
                    let secs_remainder = secs % 60;
                    output.push_str(&format!(
                        "Total Running Time: {}m {}s\n",
                        mins, secs_remainder
                    ));
                }
                output
            }
        }
    }
}