// Corrected path - LabeledFile import removed as it's unused
use crate::llm::ChatMessage;
use std::collections::HashMap;
use std::path::PathBuf;

// EditTask enum removed as it's unused.
// #[derive(Debug)]
// pub enum EditTask {
//     ProcessTarget(EditTarget),
// }

#[derive(Debug)]
pub struct ProcessingSuccess {
    pub updated_messages_for_current_task: Vec<ChatMessage>,
    // pub updated_labeled_files_map: HashMap<PathBuf, LabeledFile>, // Removed
    pub final_included_files_content_map: HashMap<PathBuf, String>, // Added field
    pub proposed_edits: Vec<ProposedEdit>, // Updated to use local ProposedEdit
    pub summary_message: String,
    pub task_summary: Option<String>,
    pub new_categorization: Option<crate::research::types::CategorizedFilePaths>, // Added field
}

use crate::research::types::ResearchSuccess;
use crate::task::Task; // Corrected path if it was crate.research...
                       // PathBuf is already imported at the top of the file.

// Added EditTarget and ProposedEdit, moved from llm_client.rs
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct EditTarget {
    pub file_path: PathBuf,
    pub start_line: usize, // 1-indexed
    pub end_line: usize,   // 1-indexed
}

#[derive(Debug, Clone)]
pub struct ProposedEdit {
    pub target: EditTarget,
    pub new_code: String,
}

#[derive(Debug)]
pub enum ProcessingSignal {
    UpdateProgress(f32), // Generic progress, could be for editing or research
    EditPlanReady {
        plan: Vec<EditTarget>,
    }, // Changed from TargetsInfo
    TargetProcessingStart {
        target_index: usize,
    }, // New signal
    TargetProcessed {
        target_index: usize,
        success: bool,
    }, // New signal
    ProcessingComplete(Result<ProcessingSuccess, String>), // Edit cycle completion
    AutoTestReprompting {
        new_task_for_reprompt: Task,
        abort_handle: tokio::task::AbortHandle,
    },
    AutoTestPassed,

    // Signals for automatic research within an edit cycle
    AutoResearchStarted, // Indicates the auto-research sub-process has started
    AutoResearchEnded,   // Indicates the auto-research sub-process has finished (success or fail)
    // AutoResearchFilesDiscovered signal removed.
    // Use AutoResearchUpdate for intermediate updates with categorization.
    AutoResearchUpdate {
        // Sent after an auto-research step discovers/categorizes files
        categorized_files: crate::research::types::CategorizedFilePaths,
    },
    // AutomaticResearchComplete signal removed as it's unused.
    // AutomaticResearchComplete {
    //     discovered_files: Result<Vec<PathBuf>, String>,
    //     research_sub_dialog_messages: Vec<ChatMessage>,
    // },

    // Signals for Research Flow, integrated into ProcessingSignal
    ResearchTurnComplete {
        // Indicates a single turn of research is done
        task_id: String,
        turn_number: usize,
        messages_this_turn: Vec<ChatMessage>, // Messages *added* in this specific turn (LLM response, system msgs for commands)
    },
    ResearchComplete(Result<ResearchSuccess, String>), // Final result of the research session

    // Signals for /question and /question-add-task-info commands
    QuestionAnswerReceived(Result<(String, String), String>), // Now returns (question, answer)
    QuestionAnswerAndAddToTaskInfoReceived(Result<(String, String), String>), // Now returns (question, answer)
    ResearchBashCommandExecuted, // Signal that a bash command was run in research
}
