use crate::llm::ChatMessage;
use chrono::Utc;
use serde::{Deserialize, Serialize};

use std::collections::HashSet;
use std::path::PathBuf;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct AppliedEdit {
    pub file_path: PathBuf,
    pub start_line: usize,
    pub end_line: usize,
    pub new_code: String,
}
#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Task {
    pub id: String,
    pub initial_user_prompt: String,
    pub initial_task_info: Option<String>,
    pub messages: Vec<ChatMessage>, // Full chat history for this task, including files messages
    pub summary: Option<String>,    // LLM-generated summary of the task's actions

    // Paths of files that were actually edited by this task.
    pub edited_files_paths: HashSet<PathBuf>,
    pub applied_edits: Vec<AppliedEdit>,
}

impl Task {
    pub fn new(initial_user_prompt: String, initial_task_info: Option<String>) -> Self {
        let id = format!("task_{}", Utc::now().timestamp_millis());
        Self {
            id,
            initial_user_prompt,
            initial_task_info,
            messages: Vec::new(),
            summary: None,
            edited_files_paths: HashSet::new(),
            applied_edits: Vec::new(),
        }
    }

    pub fn add_message(&mut self, message: ChatMessage) {
        self.messages.push(message);
    }
}