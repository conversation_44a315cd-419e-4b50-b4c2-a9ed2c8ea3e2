use crate::block_parsing::processor::{
    process_llm_response_with_blocks, BlockCount, BlockExpectation,
};
use crate::block_parsing::range_replace_block::{Parse<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RangeReplaceBlockParser};
use crate::block_parsing::traits::ParsableBlock;
use crate::block_parsing::utils;
use crate::config::AppConfig;
use crate::editor::types::ProposedEdit;
use crate::files::file_handler::{apply_edits_to_lines, LabeledFile};
use crate::files::ordered_files::OrderedFiles;
use crate::llm::{client::temporal_chat_inference, ChatMessage, ChatRole, LLMClient, MessageType};
use log::{debug, error, trace, warn};
use std::path::PathBuf;
use std::sync::Arc;

/// Public struct to hold the results of the syntax check.
#[derive(Debug)]
pub enum SyntaxCheckResult {
    /// The file is syntactically correct. The payload is the valid new content.
    Success {
        new_edits: Vec<ProposedEdit>,
        #[allow(dead_code)] // Field is part of public API, may be used by consumers
        file_content: String,
    },
    /// The file is syntactically incorrect. The payload is a detailed error message.
    Failure { error_message: String },
    /// LLM retry to fix syntax failed to produce usable blocks; use original edits.
    RetryFailedUseOriginal {
        original_edits: Vec<ProposedEdit>,
        failure_reason: String,
    },
}

/// Pre-parses a response, separating valid `range-replace` blocks from the rest of the text.
///
/// This function iterates over all code blocks in the response. For each block
/// identified as a `range-replace` block, it attempts a full validation.
///
/// - Valid blocks are parsed into `ParsedRangeReplace` and collected.
/// - The text of these valid blocks is removed from the original response string.
///
/// This leaves a string containing only non-`range-replace` text, malformed blocks,
/// and other commentary from the LLM, which is ideal for a targeted retry prompt.
///
/// # Returns
/// A tuple containing:
/// - `String`: The remaining text from the response after stripping valid blocks.
/// - `Vec<ParsedRangeReplace>`: A vector of successfully parsed and validated edits.
pub fn partition_and_strip_valid_range_replace_blocks(
    response_text: &str,
    available_files: &[LabeledFile],
) -> (String, Vec<ParsedRangeReplace>) {
    let rr_parser = RangeReplaceBlockParser;
    let extracted = utils::extract_raw_code_blocks(response_text);

    let mut good_edits = Vec::new();
    let mut remaining_text = response_text.to_string();

    let all_blocks = extracted
        .closed_blocks
        .into_iter()
        .chain(extracted.unclosed_block_at_end.into_iter());

    for block in all_blocks {
        if block.keyword == rr_parser.keyword() {
            if rr_parser
                .validate_raw_block(&block, available_files)
                .is_ok()
            {
                if let Ok(parsed_rr) =
                    rr_parser.parse_to_target_and_content(&block, available_files)
                {
                    good_edits.push(parsed_rr);
                    if let Some(pos) = remaining_text.find(&block.full_block_text) {
                        remaining_text.replace_range(pos..pos + block.full_block_text.len(), "");
                    }
                }
            }
        }
    }
    (remaining_text, good_edits)
}

/// The public entry point for the Rust syntax checker.
/// It attempts a syntax check and, if it fails, orchestrates an LLM-based retry.
pub async fn validate_and_retry_rust_edits(
    file_path: &PathBuf,
    edits_for_file: Vec<ProposedEdit>,
    ordered_files: &OrderedFiles,
    app_config: &AppConfig,
    llm_instance: Arc<dyn LLMClient>,
    historical_context: &[ChatMessage],
) -> SyntaxCheckResult {
    match perform_syntax_check(file_path, &edits_for_file, ordered_files) {
        Ok(new_content) => SyntaxCheckResult::Success {
            new_edits: edits_for_file,
            file_content: new_content,
        },
        Err(error_message) => {
            warn!(
                "Initial syntax check failed for {}: {}. Attempting to fix with LLM.",
                file_path.display(),
                error_message
            );
            handle_syntax_error_and_retry(
                file_path,
                edits_for_file,
                &error_message,
                ordered_files,
                app_config,
                llm_instance,
                historical_context,
            )
            .await
        }
    }
}

/// Performs the actual in-memory syntax check.
fn perform_syntax_check(
    file_path: &PathBuf,
    edits_for_file: &[ProposedEdit],
    ordered_files: &OrderedFiles,
) -> Result<String, String> {
    let original_content_lines = match ordered_files.get_labeled_file(file_path) {
        Some(lf) => lf.get_original_lines().clone(),
        None => {
            if edits_for_file
                .iter()
                .any(|e| e.target.start_line == 0 && e.target.end_line == 0)
            {
                Vec::new()
            } else {
                return Err(format!(
                    "Could not find file content for '{}'.",
                    file_path.display()
                ));
            }
        }
    };

    let mut new_content_lines = original_content_lines;
    if let Err(e) = apply_edits_to_lines(&mut new_content_lines, edits_for_file) {
        return Err(format!("Failed to apply edits in-memory: {}", e));
    }
    let new_content_string = new_content_lines.join("\n");

    if new_content_string.is_empty() {
        return Ok(new_content_string);
    }

    match syn::parse_file(&new_content_string) {
        Ok(_) => Ok(new_content_string),
        Err(e) => {
            let error_details = format!(
                "syn::parse_file failed for '{}'. Error: {}",
                file_path.display(),
                e
            );
            debug!("{}", error_details);
            Err(e.to_string())
        }
    }
}

/// Constructs a prompt and calls the LLM to fix syntax errors.
async fn handle_syntax_error_and_retry(
    file_path: &PathBuf,
    original_failed_edits: Vec<ProposedEdit>,
    error_message: &str,
    ordered_files: &OrderedFiles,
    app_config: &AppConfig,
    llm_instance: Arc<dyn LLMClient>,
    historical_context: &[ChatMessage],
) -> SyntaxCheckResult {
    let retry_prompt = if error_message == "cannot parse string into token stream" {
        debug!("Using delimiter-focused retry prompt for '{}' due to \"cannot parse string into token stream\" error.", file_path.display());
        construct_unparseable_token_stream_fix_prompt(
            file_path,
            &original_failed_edits,
            error_message,
        )
    } else {
        construct_retry_prompt(file_path, &original_failed_edits, error_message)
    };

    let existing_messages_for_fix_attempt = historical_context.to_vec();
    trace!(
        "Syntax fix retry for {}: Sending prompt:\n---\n{}\n---",
        file_path.display(),
        retry_prompt,
    );

    match temporal_chat_inference(
        llm_instance.as_ref(),
        &existing_messages_for_fix_attempt,
        &retry_prompt,
    )
    .await
    {
        Ok(response) => {
            trace!(
                "Syntax fix retry for {}: Received response:\n---\n{}\n---",
                file_path.display(),
                response
            );
            let mut temp_log_for_fix_parsing = Vec::new();

            let expectations = vec![BlockExpectation {
                parser: Box::new(RangeReplaceBlockParser),
                expected_count: BlockCount::Any,
            }];

            let mut history_for_llm_call_that_produced_response = historical_context.to_vec();
            history_for_llm_call_that_produced_response.push(ChatMessage {
                role: ChatRole::User,
                content: retry_prompt.clone(),
                message_type: MessageType::Text,
            });

            match process_llm_response_with_blocks(
                &response,
                &expectations,
                &retry_prompt,
                &[],
                llm_instance,
                &history_for_llm_call_that_produced_response,
                &mut temp_log_for_fix_parsing,
                app_config,
            )
            .await
            {
                Ok(processed) => {
                    let rr_parser = RangeReplaceBlockParser;
                    if let Some(blocks) = processed.successfully_parsed_blocks.get(&rr_parser.id())
                    {
                        if blocks.is_empty() {
                            let reason = "LLM did not provide any valid `range-replace` blocks in its syntax fix attempt.".to_string();
                            warn!("Syntax fix retry for {}: {}", file_path.display(), reason);
                            return SyntaxCheckResult::RetryFailedUseOriginal {
                                original_edits: original_failed_edits,
                                failure_reason: reason,
                            };
                        }

                        let new_edits: Vec<ProposedEdit> = blocks
                            .iter()
                            .filter_map(|rb| rr_parser.parse_to_target_and_content(rb, &[]).ok())
                            .map(|p_rr| ProposedEdit {
                                target: p_rr.edit_target,
                                new_code: p_rr.replacement_content,
                            })
                            .collect();

                        if new_edits.is_empty() && !blocks.is_empty() {
                            let reason = "Failed to parse any of the LLM's `range-replace` blocks after syntax fix attempt.".to_string();
                            warn!("Syntax fix retry for {}: {}", file_path.display(), reason);
                            return SyntaxCheckResult::RetryFailedUseOriginal {
                                original_edits: original_failed_edits,
                                failure_reason: reason,
                            };
                        }

                        debug!(
                            "Re-validating syntax after LLM fix for {}",
                            file_path.display()
                        );
                        match perform_syntax_check(file_path, &new_edits, ordered_files) {
                            Ok(new_content) => {
                                debug!("Syntax fix for {} was successful.", file_path.display());
                                SyntaxCheckResult::Success {
                                    new_edits,
                                    file_content: new_content,
                                }
                            }
                            Err(new_error) => {
                                let final_error_msg = format!(
                                    "LLM's attempt to fix syntax for {} failed with a new error: {}",
                                    file_path.display(), new_error
                                );
                                warn!("{}", final_error_msg);
                                SyntaxCheckResult::Failure {
                                    error_message: final_error_msg,
                                }
                            }
                        }
                    } else {
                        let reason = "LLM failed to provide any `range-replace` blocks after syntax fix attempt.".to_string();
                        warn!("Syntax fix retry for {}: {}", file_path.display(), reason);
                        SyntaxCheckResult::RetryFailedUseOriginal {
                            original_edits: original_failed_edits,
                            failure_reason: reason,
                        }
                    }
                }
                Err(e) => {
                    let reason = format!("Failed to process LLM's syntax-fix response: {}", e);
                    warn!("Syntax fix retry for {}: {}", file_path.display(), reason);
                    SyntaxCheckResult::RetryFailedUseOriginal {
                        original_edits: original_failed_edits,
                        failure_reason: reason,
                    }
                }
            }
        }
        Err(e) => {
            let reason = format!("LLM call for syntax-fix failed: {}", e);
            warn!("Syntax fix retry for {}: {}", file_path.display(), reason);
            SyntaxCheckResult::RetryFailedUseOriginal {
                original_edits: original_failed_edits,
                failure_reason: reason,
            }
        }
    }
}

/// Helper to build the detailed prompt for the syntax-fix retry.
fn construct_retry_prompt(
    file_path: &PathBuf,
    failed_edits: &[ProposedEdit],
    error_message: &str,
) -> String {
    let mut prompt = String::new();
    prompt.push_str(&format!(
        "CRITICAL: The set of edits you previously generated for the file `{}` resulted in a syntax error when checked together. Your previous plan is invalid and has been rejected.\n\n",
        file_path.display()
    ));
    prompt.push_str(&format!(
        "**Syntax Error Reported by Rust Parser:**\n```\n{}\n```\n\n",
        error_message
    ));
    prompt.push_str(
        "**These are the `ProposedEdit` blocks you provided that caused the error:**\n\n",
    );

    for edit in failed_edits {
        let edit_block = format!(
            "```range-replace\n{}\n{}-{}\n<<<<<<< RANGE\n=======\n{}\n>>>>>>> REPLACE\n```\n",
            edit.target.file_path.display(),
            edit.target.start_line,
            edit.target.end_line,
            edit.new_code
        );
        prompt.push_str(&edit_block);
    }

    prompt.push_str("\nYour task is to analyze the error and ALL of the edits above together. Then, provide a new, complete set of `range-replace` blocks for this file that fixes the syntax error. You may need to modify, merge, or completely rewrite the blocks to ensure the final file is syntactically correct.");
    prompt
}

/// Helper to build the detailed prompt for "cannot parse string into token stream" errors.
fn construct_unparseable_token_stream_fix_prompt(
    file_path: &PathBuf,
    failed_edits: &[ProposedEdit],
    error_message: &str,
) -> String {
    let mut prompt = String::new();
    prompt.push_str(&format!(
        "CRITICAL: The set of edits you previously generated for the file `{}` resulted in a fundamental parsing error: \"{}\". This often indicates issues like mismatched delimiters (parentheses, braces, brackets), unterminated string or character literals, or other severe malformations in the token structure. Your previous plan is invalid and has been rejected.\n\n",
        file_path.display(),
        error_message
    ));

    prompt.push_str(
        "**These are the `ProposedEdit` blocks you provided that caused the error:**\n\n",
    );

    for edit in failed_edits {
        let edit_block = format!(
            "```range-replace\n{}\n{}-{}\n<<<<<<< RANGE\n=======\n{}\n>>>>>>> REPLACE\n```\n",
            edit.target.file_path.display(),
            edit.target.start_line,
            edit.target.end_line,
            edit.new_code
        );
        prompt.push_str(&edit_block);
    }

    prompt.push_str("\nYour task is to meticulously review ALL of the edits above. Identify and correct the fundamental syntax problems (likely delimiter issues, malformed tokens, or unterminated literals) that led to the 'cannot parse string into token stream' error. Provide a new, complete set of `range-replace` blocks for this file that fixes these issues. Ensure the final combined code for the file will be syntactically correct and parsable by the Rust compiler.");
    prompt
}
